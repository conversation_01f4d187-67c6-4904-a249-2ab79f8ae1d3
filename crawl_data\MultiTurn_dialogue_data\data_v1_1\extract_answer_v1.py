import json


def extract_answer(base_path, input_path, output_path):

    with open(base_path, 'r', encoding='utf-8') as f:
        all_base_data = json.load(f)
    base_data = []
    for base_data_item in all_base_data:
        base_data_id = base_data_item.get('id')
        base_data_question = json.loads(base_data_item['output']).get('question')
        base_data_map = {
            'id': base_data_id,
            'question': base_data_question
        }
        base_data.append(base_data_map)


    all_data = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line in f.readlines():
            data = json.loads(line)
            all_data.append(data)
    input_data = []
    for data in all_data:
        try:
            if not data.get('answer') or data['answer'].strip() == '':
                continue
            data_answer = data.get('answer')

            data_map = {
                'id': data['id'],
                'question': data_question,
                'turns': data_turns,
            }
            input_data.append(data_map)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误，记录ID: {data.get('id', 'unknown')}, 错误: {e}")
            continue
        except KeyError as e:
            print(f"缺少必要字段，记录ID: {data.get('id', 'unknown')}, 缺少字段: {e}")
            continue

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(extracted_data, f, ensure_ascii=False, indent=4)



if __name__ == '__main__':
    base_path = r'D:\ProJects\kexue\crawl_data\0729\sci_checkout0728.json'
    input_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v1_1\资源教育-语言学习_934_doubao-1.5-pro-32k-250115_周依凡_1753867373516_data_MultiTurnDialogue.json'
    output_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v1\extracted_answer.json'
    extract_answer(input_path, output_path)