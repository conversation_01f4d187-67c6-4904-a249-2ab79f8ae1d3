<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3225131c-bd38-4584-9207-a433c9d3317c" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.cunzhi-memory/metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/.cunzhi-memory/metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cunzhi-memory/preferences.md" beforeDir="false" afterPath="$PROJECT_DIR$/.cunzhi-memory/preferences.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.cunzhi-memory/rules.md" beforeDir="false" afterPath="$PROJECT_DIR$/.cunzhi-memory/rules.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/kexue.iml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/kexue.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/run_deduplication.py" beforeDir="false" afterPath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/run_deduplication.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/test_output/medium/output_0.7/medium_test.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/test_output/medium/output_0.7/medium_test_mapping.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/test_output/medium/output_0.7/medium_test_report.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/test_output/small/output_0.8/small_test.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/test_output/small/output_0.8/small_test_mapping.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/fanzhang39/codes/Deduplicate/sentence_bert_deduplication/codes/test_output/small/output_0.8/small_test_report.txt" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zhx1Y2c7heKXRqcLw2zZNoywvg" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.biology_kmeans_analysis.executor": "Run",
    "Python.compare_counts.executor": "Run",
    "Python.data_clean.executor": "Run",
    "Python.data_crawl (1).executor": "Run",
    "Python.data_crawl (2).executor": "Run",
    "Python.data_crawl.executor": "Run",
    "Python.extract_answer.executor": "Run",
    "Python.extract_data_test.executor": "Run",
    "Python.field_extract.executor": "Run",
    "Python.find_id_occurrences.executor": "Debug",
    "Python.length_count.executor": "Run",
    "Python.merge.executor": "Run",
    "Python.merge_1.executor": "Run",
    "Python.merge_data.executor": "Run",
    "Python.prompt (1).executor": "Run",
    "Python.prompt.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "D:/ProJects/kexue/crawl_data/MultiTurn_dialogue_data/data_v1_1",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "ssh.settings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v1_1" />
      <recent name="D:\ProJects\kexue\crawl_data\0729" />
      <recent name="D:\ProJects\kexue\crawl_data\0724" />
      <recent name="D:\ProJects\kexue\crawl_data\0722" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\ProJects\kexue\crawl_data\0729\02" />
      <recent name="D:\ProJects\kexue\crawl_data\0729" />
      <recent name="D:\ProJects\kexue\crawl_data\0729\01" />
      <recent name="D:\ProJects\kexue\crawl_data\0724\length_no_limit" />
    </key>
  </component>
  <component name="RunManager" selected="Python.prompt (1)">
    <configuration name="extract_answer" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="kexue" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data/extract_answer.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="extract_answer_v1" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="kexue" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data/data_v1" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v1\extract_answer_v1.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="length_count" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="kexue" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/crawl_data/0729" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/crawl_data/0729/length_count.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="merge_data" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="kexue" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/crawl_data/0729" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/crawl_data/0729/merge_data.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="prompt (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="kexue" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data/data_v1_1" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data/data_v1_1/prompt.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.prompt (1)" />
        <item itemvalue="Python.extract_answer_v1" />
        <item itemvalue="Python.extract_answer" />
        <item itemvalue="Python.length_count" />
        <item itemvalue="Python.merge_data" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-deb605915726-JavaScript-PY-243.22562.220" />
        <option value="bundled-python-sdk-0fc6c617c4bd-9a18a617cbe4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.22562.220" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3225131c-bd38-4584-9207-a433c9d3317c" name="更改" comment="" />
      <created>1752196891929</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752196891929</updated>
      <workItem from="1752202302491" duration="4301000" />
      <workItem from="1752215295773" duration="281000" />
      <workItem from="1752220255976" duration="429000" />
      <workItem from="1752221210245" duration="243000" />
      <workItem from="1752223108174" duration="80000" />
      <workItem from="1752223203296" duration="194000" />
      <workItem from="1752223430174" duration="2625000" />
      <workItem from="1752232197336" duration="6000" />
      <workItem from="1752232220067" duration="572000" />
      <workItem from="1752232807442" duration="17357000" />
      <workItem from="1752748297231" duration="43000" />
      <workItem from="1752748354433" duration="3442000" />
      <workItem from="1753175538560" duration="74710000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/kexue$extract_answer.coverage" NAME="extract_answer 覆盖结果" MODIFIED="1753845907738" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data" />
    <SUITE FILE_PATH="coverage/kexue$data_crawl__1_.coverage" NAME="data_crawl (1) 覆盖结果" MODIFIED="1753267371036" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0723" />
    <SUITE FILE_PATH="coverage/kexue$compare_counts.coverage" NAME="compare_counts 覆盖结果" MODIFIED="1753763862793" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0729" />
    <SUITE FILE_PATH="coverage/kexue$data_crawl__2_.coverage" NAME="data_crawl (2) 覆盖结果" MODIFIED="1753352034703" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0724" />
    <SUITE FILE_PATH="coverage/kexue$biology_kmeans_analysis.coverage" NAME="biology_kmeans_analysis 覆盖结果" MODIFIED="1752206171250" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/fanzhang39/codes/qa/k-means" />
    <SUITE FILE_PATH="coverage/kexue$length_count.coverage" NAME="length_count 覆盖结果" MODIFIED="1753843363910" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0729" />
    <SUITE FILE_PATH="coverage/kexue$prompt.coverage" NAME="prompt 覆盖结果" MODIFIED="1753799142683" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data/data_v1" />
    <SUITE FILE_PATH="coverage/kexue$extract_data_test.coverage" NAME="extract_data_test 覆盖结果" MODIFIED="1753846895561" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data/data_v1" />
    <SUITE FILE_PATH="coverage/kexue$prompt__1_.coverage" NAME="prompt (1) 覆盖结果" MODIFIED="1753862279675" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/MultiTurn_dialogue_data/data_v1_1" />
    <SUITE FILE_PATH="coverage/kexue$data_crawl.coverage" NAME="data_crawl 覆盖结果" MODIFIED="1753786380785" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0729" />
    <SUITE FILE_PATH="coverage/kexue$data_clean.coverage" NAME="data_clean 覆盖结果" MODIFIED="1753186027098" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0722" />
    <SUITE FILE_PATH="coverage/kexue$merge_1.coverage" NAME="merge_1 覆盖结果" MODIFIED="1753350597691" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0724" />
    <SUITE FILE_PATH="coverage/kexue$merge_data.coverage" NAME="merge_data 覆盖结果" MODIFIED="1753843338845" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0729" />
    <SUITE FILE_PATH="coverage/kexue$field_extract.coverage" NAME="field_extract 覆盖结果" MODIFIED="1753756185454" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0729" />
    <SUITE FILE_PATH="coverage/kexue$merge.coverage" NAME="merge 覆盖结果" MODIFIED="1753352854690" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0724" />
    <SUITE FILE_PATH="coverage/kexue$find_id_occurrences.coverage" NAME="find_id_occurrences 覆盖结果" MODIFIED="1753776883952" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawl_data/0729" />
  </component>
</project>